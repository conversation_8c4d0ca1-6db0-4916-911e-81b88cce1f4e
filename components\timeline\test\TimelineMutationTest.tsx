import React from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

/**
 * Test component to verify timeline Convex mutations are working
 * This can be temporarily added to a page to test the saveChanges functionality
 */
export function TimelineMutationTest() {
  const createMonitoringEvent = useMutation(api.timeline.createMonitoringEvent);
  const updateMonitoringEvent = useMutation(api.timeline.updateMonitoringEvent);
  const deleteMonitoringEvent = useMutation(api.timeline.deleteMonitoringEvent);

  const testCreateEvent = async () => {
    try {
      const result = await createMonitoringEvent({
        projectId: "test-project-id" as any, // Replace with actual project ID
        modalityId: "test-modality-id" as any, // Replace with actual modality ID
        startTime: Date.now(),
        endTime: Date.now() + 5000,
        eventType: "test",
        severity: "normal",
        title: "Test Event",
        description: "Test event created by mutation test"
      });
      toast.success(`Created event: ${result.eventId}`);
      return result.eventId;
    } catch (error) {
      toast.error(`Failed to create event: ${error}`);
      console.error('Create event error:', error);
    }
  };

  const testUpdateEvent = async (eventId: string) => {
    try {
      await updateMonitoringEvent({
        eventId: eventId as any,
        title: "Updated Test Event",
        description: "Updated by mutation test",
        endTime: Date.now() + 10000
      });
      toast.success(`Updated event: ${eventId}`);
    } catch (error) {
      toast.error(`Failed to update event: ${error}`);
      console.error('Update event error:', error);
    }
  };

  const testDeleteEvent = async (eventId: string) => {
    try {
      await deleteMonitoringEvent({
        eventId: eventId as any
      });
      toast.success(`Deleted event: ${eventId}`);
    } catch (error) {
      toast.error(`Failed to delete event: ${error}`);
      console.error('Delete event error:', error);
    }
  };

  const runFullTest = async () => {
    toast.info('Starting timeline mutation test...');
    
    // Test create
    const eventId = await testCreateEvent();
    if (!eventId) return;

    // Wait a bit then test update
    setTimeout(async () => {
      await testUpdateEvent(eventId);
      
      // Wait a bit then test delete
      setTimeout(async () => {
        await testDeleteEvent(eventId);
        toast.success('Timeline mutation test completed!');
      }, 2000);
    }, 2000);
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Timeline Mutation Test</h3>
      <div className="space-x-2">
        <Button onClick={testCreateEvent} variant="outline">
          Test Create
        </Button>
        <Button onClick={() => testUpdateEvent('test-id')} variant="outline">
          Test Update
        </Button>
        <Button onClick={() => testDeleteEvent('test-id')} variant="outline">
          Test Delete
        </Button>
        <Button onClick={runFullTest} className="bg-blue-600 hover:bg-blue-700">
          Run Full Test
        </Button>
      </div>
      <p className="text-sm text-gray-600 mt-2">
        This component tests the Convex mutations for timeline events.
        Check the console and toast notifications for results.
      </p>
    </div>
  );
}
