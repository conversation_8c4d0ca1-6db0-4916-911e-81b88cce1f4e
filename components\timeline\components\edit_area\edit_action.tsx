import React, { FC, useLayoutEffect, useRef, useState } from 'react';
import { TimelineEvent, TimelineRow } from '../../interface/action';
import { CommonProp } from '../../interface/common_prop';
import { DEFAULT_ADSORPTION_DISTANCE, DEFAULT_MOVE_GRID } from '../../interface/const';
import { prefix } from '../../utils/deal_class_prefix';
import { getScaleCountByPixel, parserTimeToPixel, parserTimeToTransform, parserTransformToTime } from '../../utils/deal_data';
import { RowDnd } from '../row_rnd/row_rnd';
import { RndDragCallback, RndDragEndCallback, RndDragStartCallback, RndResizeCallback, RndResizeEndCallback, RndResizeStartCallback, RowRndApi } from '../row_rnd/row_rnd_interface';
import { DragLineData } from './drag_lines';
import './edit_action.css';

export type EditActionProps = CommonProp & {
  row: TimelineRow;
  action: TimelineEvent;
  dragLineData: DragLineData;
  setEditorData: (params: TimelineRow[]) => void;
  handleTime: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => number;
  areaRef?: React.RefObject<HTMLDivElement> | null;
  /** Set scroll left */
  deltaScrollLeft?: (delta: number) => void;
};

export const EditAction: FC<EditActionProps> = ({
  editorData,
  row,
  action,
  effects,
  rowHeight,
  scale,
  scaleWidth,
  scaleSplitCount,
  startLeft,
  gridSnap,
  disableDrag,

  scaleCount,
  maxScaleCount,
  setScaleCount,
  onActionMoveStart,
  onActionMoving,
  onActionMoveEnd,
  onActionResizeStart,
  onActionResizeEnd,
  onActionResizing,

  dragLineData,
  setEditorData,
  onClickAction,
  onClickActionOnly,
  onDoubleClickAction,
  onContextMenuAction,
  getActionRender,
  handleTime,
  areaRef,
  deltaScrollLeft,
}) => {
  const rowRnd = useRef<RowRndApi>(null);
  const isDragWhenClick = useRef(false);
  const [isResizing, setIsResizing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const resizeThrottleRef = useRef<number | null>(null);
  const dragThrottleRef = useRef<number | null>(null);
  const { id, maxEnd, minStart, end, start, selected, flexible = true, movable = true, effectId } = action;

  // Get the maximum/minimum pixel range
  const leftLimit = parserTimeToPixel(minStart || 0, {
    startLeft: startLeft!,
    scale: scale!,
    scaleWidth: scaleWidth!,
  });
  const rightLimit = Math.min(
    maxScaleCount! * scaleWidth! + startLeft!, // Limit the movement range according to maxScaleCount
    parserTimeToPixel(maxEnd || Number.MAX_VALUE, {
      startLeft: startLeft!,
      scale: scale!,
      scaleWidth: scaleWidth!,
    }),
  );

  // Initialize the action's coordinate data
  const [transform, setTransform] = useState(() => {
    return parserTimeToTransform({ start, end }, { startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! });
  });

  useLayoutEffect(() => {
    setTransform(parserTimeToTransform({ start, end }, { startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! }));
  }, [end, start, startLeft, scaleWidth, scale]);

  // Cleanup animation frames on unmount
  useLayoutEffect(() => {
    return () => {
      if (resizeThrottleRef.current) {
        cancelAnimationFrame(resizeThrottleRef.current);
      }
      if (dragThrottleRef.current) {
        cancelAnimationFrame(dragThrottleRef.current);
      }
    };
  }, []);

  // Configure the drag grid to align with the properties
  const gridSize = scaleWidth! / scaleSplitCount!;

  // Action name
  const classNames = ['action'];
  if (movable) classNames.push('action-movable');
  if (selected) classNames.push('action-selected');
  if (flexible) classNames.push('action-flexible');
  // Apply effect-based styling if effects are available
  if (effects && effectId && effects[effectId]) classNames.push(`action-effect-${effectId}`);
  // Add resize/drag state classes to prevent visual artifacts
  if (isResizing) classNames.push('timeline-action-resizing');
  if (isDragging) classNames.push('timeline-action-dragging');

  /** Calculate scale count */
  const handleScaleCount = (left: number, width: number) => {
    const curScaleCount = getScaleCountByPixel(left + width, {
      startLeft: startLeft!,
      scaleCount,
      scaleWidth: scaleWidth!,
    });
    if (curScaleCount !== scaleCount) setScaleCount(curScaleCount);
  };

  //#region [rgba(100,120,156,0.08)] Callback
  const handleDragStart: RndDragStartCallback = () => {
    setIsDragging(true);
    // Add CSS class to row to prevent other elements from shifting
    if (areaRef?.current) {
      const rowElement = areaRef.current.querySelector(`[data-row-id="${row.id}"]`);
      if (rowElement) {
        rowElement.classList.add('timeline-row-dragging');
      }
    }
    if(onActionMoveStart)
      onActionMoveStart({ action: action!, row: row!});
  };

  const handleDrag: RndDragCallback = ({ left, width }) => {
    isDragWhenClick.current = true;

    // Always update visual position immediately for smooth interaction
    setTransform({ left, width });

    // Throttle expensive operations (callbacks and scale count updates)
    if (dragThrottleRef.current) {
      cancelAnimationFrame(dragThrottleRef.current);
    }

    dragThrottleRef.current = requestAnimationFrame(() => {
      if (onActionMoving) {
        const { start, end } = parserTransformToTime({ left, width }, { scaleWidth: scaleWidth!, scale: scale!, startLeft: startLeft! });
        const result = onActionMoving({ action, row, start, end });
        if (result === false) return false;
      }
      handleScaleCount(left, width);
    });
  };

  const handleDragEnd: RndDragEndCallback = ({ left, width }) => {
    setIsDragging(false);

    // Cancel any pending throttled operations
    if (dragThrottleRef.current) {
      cancelAnimationFrame(dragThrottleRef.current);
      dragThrottleRef.current = null;
    }

    // Remove CSS class from row
    if (areaRef?.current) {
      const rowElement = areaRef.current.querySelector(`[data-row-id="${row.id}"]`);
      if (rowElement) {
        rowElement.classList.remove('timeline-row-dragging');
      }
    }

    // Calculate time
    const { start, end } = parserTransformToTime({ left, width }, { scaleWidth: scaleWidth!, scale: scale!, startLeft: startLeft! });

    // Set data - create new array to trigger React re-render properly
    const newEditorData = editorData.map(rowItem => {
      if (rowItem.id === row.id) {
        return {
          ...rowItem,
          actions: rowItem.actions.map(actionItem => {
            if (actionItem.id === id) {
              return { ...actionItem, start, end };
            }
            return actionItem;
          })
        };
      }
      return rowItem;
    });
    setEditorData(newEditorData);

    // Execute callback
    if (onActionMoveEnd) onActionMoveEnd({ action: action!, row: row!, start, end });
  };

  const handleResizeStart: RndResizeStartCallback = (dir) => {
    setIsResizing(true);
    // Add CSS class to row to prevent other elements from shifting
    if (areaRef?.current) {
      const rowElement = areaRef.current.querySelector(`[data-row-id="${row.id}"]`);
      if (rowElement) {
        rowElement.classList.add('timeline-row-resizing');
      }
    }
    if(onActionResizeStart)
      onActionResizeStart({ action: action!, row: row!, dir });
  };

  const handleResizing: RndResizeCallback = (dir, { left, width }) => {
    isDragWhenClick.current = true;

    // Always update visual position immediately for smooth interaction
    setTransform({ left, width });

    // Throttle expensive operations (callbacks and scale count updates)
    if (resizeThrottleRef.current) {
      cancelAnimationFrame(resizeThrottleRef.current);
    }

    resizeThrottleRef.current = requestAnimationFrame(() => {
      if (onActionResizing) {
        const { start, end } = parserTransformToTime({ left, width }, { scaleWidth: scaleWidth!, scale: scale!, startLeft: startLeft! });
        const result = onActionResizing({ action, row, start, end, dir });
        if (result === false) return false;
      }
      handleScaleCount(left, width);
    });
  };

  const handleResizeEnd: RndResizeEndCallback = (dir, { left, width }) => {
    setIsResizing(false);

    // Cancel any pending throttled operations
    if (resizeThrottleRef.current) {
      cancelAnimationFrame(resizeThrottleRef.current);
      resizeThrottleRef.current = null;
    }

    // Remove CSS class from row
    if (areaRef?.current) {
      const rowElement = areaRef.current.querySelector(`[data-row-id="${row.id}"]`);
      if (rowElement) {
        rowElement.classList.remove('timeline-row-resizing');
      }
    }

    // Calculate time
    const { start, end } = parserTransformToTime({ left, width }, { scaleWidth: scaleWidth!, scale: scale!, startLeft: startLeft! });

    // Set data - create new array to trigger React re-render properly
    const newEditorData = editorData.map(rowItem => {
      if (rowItem.id === row.id) {
        return {
          ...rowItem,
          actions: rowItem.actions.map(actionItem => {
            if (actionItem.id === id) {
              return { ...actionItem, start, end };
            }
            return actionItem;
          })
        };
      }
      return rowItem;
    });
    setEditorData(newEditorData);

    // Trigger callback
    if (onActionResizeEnd) onActionResizeEnd({ action: action!, row: row!, start, end, dir });
  };
  //#endregion

  const nowAction = {
    ...action,
    ...parserTransformToTime({ left: transform.left, width: transform.width }, { startLeft: startLeft!, scaleWidth: scaleWidth!, scale: scale! }),
  };

  const nowRow: TimelineRow = {
    ...row,
    actions: [...row.actions],
  };
  if (row.actions.includes(action)) {
    nowRow.actions[row.actions.indexOf(action)] = nowAction;
  }

  return (
    <RowDnd
      ref={rowRnd}
      parentRef={areaRef || undefined}
      start={startLeft}
      left={transform.left}
      width={transform.width}
      grid={(gridSnap && gridSize) || DEFAULT_MOVE_GRID}
      adsorptionDistance={gridSnap ? Math.max((gridSize || DEFAULT_MOVE_GRID) / 2, DEFAULT_ADSORPTION_DISTANCE) : DEFAULT_ADSORPTION_DISTANCE}
      adsorptionPositions={dragLineData.assistPositions}
      bounds={{
        left: leftLimit,
        right: rightLimit,
      }}
      edges={{
        left: !disableDrag && flexible && `.${prefix('action-left-stretch')}`,
        right: !disableDrag && flexible && `.${prefix('action-right-stretch')}`,
      }}
      enableDragging={!disableDrag && movable}
      enableResizing={!disableDrag && flexible}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
      onResizeStart={handleResizeStart}
      onResize={handleResizing}
      onResizeEnd={handleResizeEnd}
      deltaScrollLeft={deltaScrollLeft}
    >
      <div
        onMouseDown={() => {
          isDragWhenClick.current = false;
        }}
        onClick={(e) => {
          let time: number | undefined;
          if (onClickAction) {
            time = handleTime(e);
            onClickAction(e, { row, action, time: time });
          }
          if (!isDragWhenClick.current && onClickActionOnly) {
            if (!time) time = handleTime(e);
            onClickActionOnly(e, { row, action, time: time });
          }
        }}
        onDoubleClick={(e) => {
          if (onDoubleClickAction) {
            const time = handleTime(e);
            onDoubleClickAction(e, { row, action, time: time });
          }
        }}
        onContextMenu={(e) => {
          if (onContextMenuAction) {
            const time = handleTime(e);
            onContextMenuAction(e, { row, action, time: time });
          }
        }}
       // className={prefix((classNames || []).join(' '))}
        style={{ height: rowHeight }}
      >
        {getActionRender && getActionRender(nowAction, nowRow)}
        {flexible && <div className={prefix('action-left-stretch')} />}
        {flexible && <div className={prefix('action-right-stretch')} />}
      </div>
    </RowDnd>
  );
};

