"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Doc } from '@/convex/_generated/dataModel';
import { TimelineEvent, TimelineModality } from '@/types/timeline';

// Types for the context
export interface ProjectContextType {
  // Current project data
  currentProject: Doc<"projects"> | null;
  currentProjectId: Id<"projects"> | null;
  currentSession: Doc<"streamSessions"> | null;
  currentSessionId: Id<"streamSessions"> | null;
  currentUser: Doc<"users"> | null;
  currentUserId: Id<"users"> | null;

  // Project-related data
  modalities: TimelineModality[];
  events: TimelineEvent[];

  // Loading states
  isLoadingProject: boolean;
  isLoadingSession: boolean;
  isLoadingModalities: boolean;
  isLoadingEvents: boolean;

  // Actions
  setCurrentProjectId: (projectId: Id<"projects"> | null) => void;
  setCurrentSessionId: (sessionId: Id<"streamSessions"> | null) => void;
  setCurrentUserId: (userId: Id<"users"> | null) => void;

  // Application state
  applicationState: 'live-monitoring' | 'historical-review' | 'editing' | 'reporting';
  setApplicationState: (state: 'live-monitoring' | 'historical-review' | 'editing' | 'reporting') => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export interface ProjectProviderProps {
  children: ReactNode;
  initialProjectId?: Id<"projects">;
  initialApplicationState?: 'live-monitoring' | 'historical-review' | 'editing' | 'reporting';
}

export function ProjectProvider({ 
  children, 
  initialProjectId,
  initialApplicationState = 'live-monitoring'
}: ProjectProviderProps) {
  // State management
  const [currentProjectId, setCurrentProjectId] = useState<Id<"projects"> | null>(initialProjectId || null);
  const [currentSessionId, setCurrentSessionId] = useState<Id<"streamSessions"> | null>(null);
  const [currentUserId, setCurrentUserId] = useState<Id<"users"> | null>(null);
  const [applicationState, setApplicationState] = useState<'live-monitoring' | 'historical-review' | 'editing' | 'reporting'>(initialApplicationState);

  // Fetch current project data
  const currentProject = useQuery(
    api.projects.getProject,
    currentProjectId ? { projectId: currentProjectId } : "skip"
  );

  // Fetch current session data
  const currentSession = useQuery(
    api.streamSessions?.getSession,
    currentSessionId ? { sessionId: currentSessionId } : "skip"
  );

  // Fetch current user data
  const currentUser = useQuery(
    api.users?.getUser,
    currentUserId ? { userId: currentUserId } : "skip"
  );

  // Fetch project modalities and events
  const modalities = useQuery(
    api.timeline.getProjectModalities,
    currentProjectId ? { projectId: currentProjectId } : "skip"
  );

  const events = useQuery(
    api.timeline.getProjectEvents,
    currentProjectId ? { projectId: currentProjectId } : "skip"
  );

  // Auto-detect test project if no project is set
  const testProject = useQuery(api.seed.getTestProject);
  
  useEffect(() => {
    if (!currentProjectId && testProject?._id) {
      setCurrentProjectId(testProject._id);
    }
  }, [currentProjectId, testProject]);

  // Auto-detect test user if no user is set
  useEffect(() => {
    if (!currentUserId && testProject?.createdBy) {
      // Only set if the user actually exists
      setCurrentUserId(testProject.createdBy);
    }
  }, [currentUserId, testProject]);

  // Auto-detect active session for current project
  useEffect(() => {
    if (currentProjectId && !currentSessionId && applicationState === 'live-monitoring') {
      // In a real app, we would query for active sessions
      // For now, we'll create a placeholder session ID based on the project
      // This should be replaced with proper session management
    }
  }, [currentProjectId, currentSessionId, applicationState]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo<ProjectContextType>(() => {
    console.log('[ProjectProvider] CONTEXT VALUE RECREATED - Dependencies changed')
    return {
      // Current data
      currentProject: currentProject || null,
      currentProjectId,
      currentSession: currentSession || null,
      currentSessionId,
      currentUser: currentUser || null,
      currentUserId,

      // Project-related data
      modalities,
      events: events as any, // Type assertion to fix the type mismatch

      // Loading states
      isLoadingProject: currentProjectId !== null && currentProject === undefined,
      isLoadingSession: currentSessionId !== null && currentSession === undefined,
      isLoadingModalities: currentProjectId !== null && modalities.length === 0,
      isLoadingEvents: currentProjectId !== null && events.length === 0,

      // Actions
      setCurrentProjectId,
      setCurrentSessionId,
      setCurrentUserId,

      // Application state
      applicationState,
      setApplicationState,
    }
  }, [
    currentProject,
    currentProjectId,
    currentSession,
    currentSessionId,
    currentUser,
    currentUserId,
    modalities,
    events,
    applicationState,
    // Don't include setState functions as they're stable
  ]);

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext(): ProjectContextType {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
}

// Convenience hooks for common use cases
export function useCurrentProject() {
  const { currentProject, currentProjectId, isLoadingProject } = useProjectContext();
  return { project: currentProject, projectId: currentProjectId, isLoading: isLoadingProject };
}

export function useCurrentSession() {
  const { currentSession, currentSessionId, isLoadingSession } = useProjectContext();
  return { session: currentSession, sessionId: currentSessionId, isLoading: isLoadingSession };
}

export function useCurrentUser() {
  const { currentUser, currentUserId } = useProjectContext();
  return { user: currentUser, userId: currentUserId };
}

export function useProjectData() {
  const { modalities, events, isLoadingModalities, isLoadingEvents } = useProjectContext();
  return {
    modalities,
    events,
    isLoading: isLoadingModalities || isLoadingEvents
  };
}
