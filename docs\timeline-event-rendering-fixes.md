# Timeline Event Rendering Fixes

## Issues Identified

Based on the user's feedback, there were several problems with timeline event rendering:

1. **Only bars showing regardless of event type** - Severity-based renderers not working
2. **Diamond renderer not working correctly** - Should show for small pixel width, not time duration
3. **Both bars and diamonds showing** - <PERSON> should replace the bar entirely
4. **Incorrect pixel width calculation** - Using inconsistent formulas across renderers

## Root Causes

### 1. **Inconsistent Pixel Width Calculation**
Different renderers were using different formulas:
```typescript
// BEFORE: Inconsistent calculations
const width = (action.end - action.start) * scale;  // Some renderers
const pixelWidth = durationInSeconds * scale;      // Other renderers
```

### 2. **Incorrect Diamond Threshold Logic**
The diamond renderer was being triggered by time duration rather than pixel width:
```typescript
// BEFORE: Wrong logic
if (width < 30) {  // This could be time-based, not pixel-based
  return DiamondEventRenderer;
}
```

### 3. **Missing Debug Information**
No logging to understand which renderer was being selected and why.

## Fixes Implemented

### 1. **Standardized Pixel Width Calculation**

Updated all renderers to use consistent pixel width calculation:

```typescript
// AFTER: Consistent calculation everywhere
const durationInSeconds = action.end - action.start;
const pixelWidth = durationInSeconds * scale;
```

Applied to:
- `BaseActionRenderer`
- `CompactEventRenderer` 
- `ModalityEventRenderer`
- `getActionRenderer`

### 2. **Enhanced Diamond Renderer**

Fixed the DiamondEventRenderer positioning and styling:

```typescript
export function DiamondEventRenderer(props: NFMActionRendererProps) {
  const { action, row, isSelected, isHovered, scale, height } = props;
  
  // Calculate position - center the diamond on the event start time
  const leftPosition = action.start * scale;
  const topPosition = (height - 20) / 2; // Center vertically in the row
  
  return (
    <div
      className="timeline-action timeline-action-diamond"
      style={{
        backgroundColor: modalityColor,
        borderColor: modalityColorDark,
        border: `2px solid ${modalityColorDark}`,
        left: `${leftPosition}px`,
        top: `${topPosition}px`,
      }}
    >
      <div className="timeline-action-content">
        {severity === 'critical' ? '!' :
         severity === 'warning' ? '⚠' :
         action.title?.charAt(0) || 'E'}
      </div>
    </div>
  );
}
```

### 3. **Added Debug Logging**

Added comprehensive logging to understand renderer selection:

```typescript
export function getActionRenderer(action, row, scale) {
  const durationInSeconds = action.end - action.start;
  const pixelWidth = durationInSeconds * scale;
  
  console.debug('[getActionRenderer] Event:', action.id, 'Duration:', durationInSeconds, 'Scale:', scale, 'PixelWidth:', pixelWidth);
  
  if (pixelWidth < 30) {
    console.debug('[getActionRenderer] Using DiamondEventRenderer for short event');
    return DiamondEventRenderer;
  }
  
  // ... severity checks with logging
}
```

### 4. **Improved Renderer Selection Logic**

Enhanced the renderer selection with clear priority:

```typescript
export function getActionRenderer(action, row, scale) {
  // 1. FIRST: Check pixel width for diamond renderer
  if (pixelWidth < 30) {
    return DiamondEventRenderer;
  }
  
  // 2. SECOND: Check severity-based renderers
  if (severity === 'critical' || eventType === 'critical') {
    return CriticalEventRenderer;
  }
  if (severity === 'warning' || eventType === 'warning') {
    return WarningEventRenderer;
  }
  if (eventType === 'info') {
    return InfoEventRenderer;
  }
  
  // 3. THIRD: Default to modality renderer
  return ModalityEventRenderer;
}
```

## Expected Behavior After Fixes

### 1. **Diamond Renderer for Small Events**
- ✅ Events with pixel width < 30px should show diamond only
- ✅ Diamond should be centered on event start time
- ✅ Diamond should show appropriate icon (!, ⚠, or first letter)
- ✅ No bar should be visible for diamond events

### 2. **Severity-Based Rendering**
- ✅ Critical events should use CriticalEventRenderer (red with alert icon)
- ✅ Warning events should use WarningEventRenderer (amber with warning icon)
- ✅ Info events should use InfoEventRenderer (blue with info icon)

### 3. **Consistent Pixel Width**
- ✅ All renderers use same calculation: `(action.end - action.start) * scale`
- ✅ Diamond threshold is based on actual pixel width, not time duration

### 4. **Debug Information**
- ✅ Console logs show which renderer is selected and why
- ✅ Pixel width calculations are logged for debugging

## Testing Instructions

### 1. **Test Diamond Renderer**
1. Create a very short event (< 0.3 seconds at scale 100)
2. Should see diamond shape only, no bar
3. Console should log "Using DiamondEventRenderer for short event"

### 2. **Test Severity Rendering**
1. Create events with different severity levels
2. Critical events should be red with "!" icon
3. Warning events should be amber with "⚠" icon
4. Console should log which renderer is selected

### 3. **Test Scale Changes**
1. Zoom in/out to change scale
2. Events should switch between diamond and bar based on pixel width
3. Console should show pixel width calculations

### 4. **Test Resize Behavior**
1. Resize an event to make it very short
2. Should automatically switch to diamond renderer
3. Should switch back to bar when made longer

## Files Modified

- `components/timeline/effects/eventRenderers.tsx`: 
  - Standardized pixel width calculations
  - Enhanced DiamondEventRenderer positioning
  - Added debug logging
  - Improved renderer selection logic

## Next Steps

1. **Test the fixes** with various event types and sizes
2. **Remove debug logs** once rendering is confirmed working
3. **Verify CSS styling** for diamond renderer positioning
4. **Check for conflicts** with timeline engine default rendering

The key insight is that the diamond renderer should be based on **pixel width**, not time duration, and should completely replace the bar renderer for small events.
