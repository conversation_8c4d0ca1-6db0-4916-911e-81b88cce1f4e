/**
 * React hook for managing timeline data and state (Direct Integration)
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Doc, Id } from '@/convex/_generated/dataModel';
import { TimelineRow, TimelineEvent } from '@/components/timeline/interface/timeline';
import {
  NFMTimelineEffect,
  TimelineDataTransformResult,
  TimelineChangeResult
} from '@/types/timelineEditor';

/**
 * Deep clone utility using native JavaScript
 */
function deepClone<T>(obj: T): T {
  if (typeof structuredClone !== 'undefined') {
    return structuredClone(obj);
  }
  // Fallback for older browsers
  return JSON.parse(JSON.stringify(obj));
}

export interface UseTimelineDataOptions {
  // Auto-save configuration
  autoSave?: boolean;
  autoSaveDelay?: number; // milliseconds

  // Validation options
  validateOnChange?: boolean;
  strictValidation?: boolean;

  // Performance options
  debounceDelay?: number;
  maxUndoSteps?: number;

  // Feature flags
  enableUndo?: boolean;
  enableChangeTracking?: boolean;

  // Convex mutations for persistence
  onEventCreate?: (event: Partial<Doc<"monitoringEvents">>, modalityId: string) => Promise<void>;
  onEventUpdate?: (event: Partial<Doc<"monitoringEvents">>) => Promise<void>;
  onEventDelete?: (eventId: string) => Promise<void>;
}

export interface UseTimelineDataReturn {
  // Transformed data
  editorData: TimelineRow[];
  effects: Record<string, NFMTimelineEffect>;
  duration: number;
  
  // State management
  isDirty: boolean;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  
  // Change tracking
  hasUnsavedChanges: boolean;
  lastSaved: Date | null;
  changeCount: number;
  
  // Undo/Redo
  canUndo: boolean;
  canRedo: boolean;
  
  // Actions
  updateData: (newData: TimelineRow[], immediate?: boolean) => void;
  saveChanges: () => Promise<void>;
  discardChanges: () => void;
  undo: () => void;
  redo: () => void;
  
  // Validation
  validate: () => { isValid: boolean; errors: string[]; warnings: string[] };
  
  // Utilities
  getChanges: () => TimelineChangeResult;
  exportData: () => string;
  importData: (data: string) => boolean;
}

export function useTimelineData(
  modalities: Doc<"modalityConfigs">[],
  events: Doc<"monitoringEvents">[],
  options: UseTimelineDataOptions = {}
): UseTimelineDataReturn {
  const {
    autoSave = false,
    autoSaveDelay = 2000,
    validateOnChange = true,
    strictValidation = false,
    debounceDelay = 300,
    maxUndoSteps = 50,
    enableUndo = true,
    enableChangeTracking = true,
    onEventCreate,
    onEventUpdate,
    onEventDelete
  } = options;

  // Core state
  const [editorData, setEditorData] = useState<TimelineRow[]>([]);
  const [effects, setEffects] = useState<Record<string, NFMTimelineEffect>>({});
  const [duration, setDuration] = useState<number>(3600);
  
  // Validation state
  const [isValid, setIsValid] = useState<boolean>(true);
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  
  // Change tracking state
  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [changeCount, setChangeCount] = useState<number>(0);
  
  // Undo/Redo state
  const [undoStack, setUndoStack] = useState<TimelineRow[][]>([]);
  const [redoStack, setRedoStack] = useState<TimelineRow[][]>([]);

  // Refs for tracking
  const originalDataRef = useRef<TimelineRow[]>([]);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>(null);
  
  // Direct conversion of NFM data to timeline format
  const timelineData = useMemo(() => {
    try {
      // Convert modalities to timeline rows
      const editorData: TimelineRow[] = modalities
        .filter(modality => modality.isActive) // Only include active modalities
        .map(modality => {
          // Filter events for this modality
          const modalityEvents = events.filter(event => event.modalityId === modality._id);

          // Convert events to timeline format
          const actions: TimelineEvent[] = modalityEvents.map(event => ({
            ...event,
            id: event._id,
            start: event.startTime,
            end: event.endTime || event.startTime + 1, // Default 1 second duration
            effectId: `modality-${modality._id}`,
            selected: false,
            flexible: true,
            movable: true,
            disable: false,
            minStart: 0,
            maxEnd: Number.MAX_VALUE,
            modalityName: modality.name,
            modalityColor: modality.colorCode
          }));

          return {
            ...modality,
            id: modality._id,
            displayName: modality.displayName || modality.name, // Ensure displayName is always a string
            actions,
            rowHeight: 40,
            selected: false,
            classNames: [`modality-${modality.name.toLowerCase().replace(/\s+/g, '-')}`],
            isVisible: true // Default to visible
          };
        });

      // Calculate duration
      const maxEndTime = events.length > 0
        ? Math.max(...events.map(event => event.endTime || event.startTime + 1))
        : 3600;
      const duration = Math.max(maxEndTime * 1.1, 3600); // Add 10% padding

      // Basic validation
      const validation = {
        isValid: true,
        errors: [] as string[],
        warnings: [] as string[]
      };

      // Validate data
      editorData.forEach((row, rowIndex) => {
        if (!row.id) {
          validation.errors.push(`Row ${rowIndex} missing ID`);
          validation.isValid = false;
        }

        row.actions.forEach((action, actionIndex) => {
          if (!action.id) {
            validation.errors.push(`Row ${rowIndex}, Action ${actionIndex} missing ID`);
            validation.isValid = false;
          }
          if (action.start >= action.end) {
            validation.errors.push(`Row ${rowIndex}, Action ${actionIndex} has invalid time range`);
            validation.isValid = false;
          }
        });
      });

      // Create effects object for styling system
      const effects: Record<string, NFMTimelineEffect> = {};
      console.log('Creating effects for modalities:', modalities);
      modalities.forEach(modality => {
        if (modality.isActive) {
          effects[`modality-${modality._id}`] = {
            id: `modality-${modality._id}`,
            name: modality.displayName || modality.name,
            color: modality.colorCode,
            category: 'modality' as const,
            customRenderer: true
          };
        }
      });

      return {
        editorData,
        effects,
        duration,
        validation
      } as TimelineDataTransformResult;
    } catch (error) {
      console.error('Error processing timeline data:', error);
      return {
        editorData: [],
        effects: {},
        duration: 3600,
        validation: {
          isValid: false,
          errors: ['Failed to process timeline data'],
          warnings: []
        }
      } as TimelineDataTransformResult;
    }
  }, [modalities, events]);

  // Update state when timeline data changes
  useEffect(() => {
    setEditorData(timelineData.editorData);
    setEffects(timelineData.effects);
    setDuration(timelineData.duration);
    setIsValid(timelineData.validation.isValid);
    setErrors(timelineData.validation.errors);
    setWarnings(timelineData.validation.warnings);

    // Reset change tracking when source data changes
    originalDataRef.current = deepClone(timelineData.editorData);
    setIsDirty(false);
    setHasUnsavedChanges(false);
    setChangeCount(0);

    // Clear undo/redo stacks
    if (enableUndo) {
      setUndoStack([]);
      setRedoStack([]);
    }
  }, [timelineData, enableUndo]);

  // Validation function
  const validate = useCallback(() => {
    const result = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[]
    };

    // Basic validation
    editorData.forEach((row, rowIndex) => {
      if (!row.id) {
        result.errors.push(`Row ${rowIndex} missing ID`);
        result.isValid = false;
      }

      row.actions.forEach((action, actionIndex) => {
        if (!action.id) {
          result.errors.push(`Row ${rowIndex}, Action ${actionIndex} missing ID`);
          result.isValid = false;
        }
        if (action.start >= action.end) {
          result.errors.push(`Row ${rowIndex}, Action ${actionIndex} has invalid time range`);
          result.isValid = false;
        }
        if (action.start < 0) {
          result.errors.push(`Row ${rowIndex}, Action ${actionIndex} has negative start time`);
          result.isValid = false;
        }
      });

      // Check for overlapping actions in the same row
      const sortedActions = [...row.actions].sort((a, b) => a.start - b.start);
      for (let i = 0; i < sortedActions.length - 1; i++) {
        const current = sortedActions[i];
        const next = sortedActions[i + 1];
        if (current.end > next.start) {
          result.warnings.push(`Row ${rowIndex} has overlapping events: ${current.id} and ${next.id}`);
        }
      }
    });

    setIsValid(result.isValid);
    setErrors(result.errors);
    setWarnings(result.warnings);

    return result;
  }, [editorData]);

  // Placeholder for saveChanges - will be defined after getChanges

  // Update data with change tracking and validation
  const updateData = useCallback((newData: TimelineRow[], immediate = false) => {
    // For immediate updates (like final resize/drag end), skip debouncing
    if (immediate) {
      // Add to undo stack if enabled
      if (enableUndo && editorData.length > 0) {
        setUndoStack(prev => {
          const newStack = [...prev, deepClone(editorData)];
          return newStack.slice(-maxUndoSteps); // Limit stack size
        });
        setRedoStack([]); // Clear redo stack on new change
      }

      // Update data immediately
      setEditorData(newData);

      // Update change tracking
      setIsDirty(true);
      setHasUnsavedChanges(true);
      setChangeCount(prev => prev + 1);

      // Validate if enabled
      if (validateOnChange) {
        validate();
      }

      // Auto-save if enabled - will be handled by separate effect
      return;
    }

    // For regular updates, use debouncing
    // Clear any pending debounced operations
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      // Add to undo stack if enabled
      if (enableUndo && editorData.length > 0) {
        setUndoStack(prev => {
          const newStack = [...prev, deepClone(editorData)];
          return newStack.slice(-maxUndoSteps); // Limit stack size
        });
        setRedoStack([]); // Clear redo stack on new change
      }

      // Update data
      setEditorData(newData);

      // Update change tracking
      setIsDirty(true);
      setHasUnsavedChanges(true);
      setChangeCount(prev => prev + 1);

      // Validate if enabled
      if (validateOnChange) {
        validate();
      }

      // Auto-save if enabled - will be handled by separate effect
    }, debounceDelay);
  }, [editorData, enableUndo, maxUndoSteps, validateOnChange, validate, debounceDelay]);

  // Discard changes
  const discardChanges = useCallback(() => {
    setEditorData(deepClone(originalDataRef.current));
    setIsDirty(false);
    setHasUnsavedChanges(false);
    setChangeCount(0);
    
    if (enableUndo) {
      setUndoStack([]);
      setRedoStack([]);
    }
  }, [enableUndo]);

  // Undo operation
  const undo = useCallback(() => {
    if (!enableUndo || undoStack.length === 0) return;
    
    const previousState = undoStack[undoStack.length - 1];
    const newUndoStack = undoStack.slice(0, -1);
    
    // Add current state to redo stack
    setRedoStack(prev => [...prev, deepClone(editorData)]);
    setUndoStack(newUndoStack);
    setEditorData(deepClone(previousState));
    
    setChangeCount(prev => prev + 1);
  }, [enableUndo, undoStack, editorData]);

  // Redo operation
  const redo = useCallback(() => {
    if (!enableUndo || redoStack.length === 0) return;
    
    const nextState = redoStack[redoStack.length - 1];
    const newRedoStack = redoStack.slice(0, -1);
    
    // Add current state to undo stack
    setUndoStack(prev => [...prev, deepClone(editorData)]);
    setRedoStack(newRedoStack);
    setEditorData(deepClone(nextState));
    
    setChangeCount(prev => prev + 1);
  }, [enableUndo, redoStack, editorData]);

  // Get changes between current and original data
  const getChanges = useCallback((): TimelineChangeResult => {
    if (!enableChangeTracking) {
      return {
        hasChanges: false,
        createdEvents: [],
        updatedEvents: [],
        deletedEventIds: [],
        movedEvents: []
      };
    }

    const createdEvents: Partial<Doc<"monitoringEvents">>[] = [];
    const updatedEvents: Partial<Doc<"monitoringEvents">>[] = [];
    const deletedEventIds: Id<"monitoringEvents">[] = [];
    const movedEvents: Array<{
      eventId: string;
      fromModalityId: string;
      toModalityId: string;
    }> = [];

    // Create maps for efficient lookup
    const originalActionMap = new Map<Id<"monitoringEvents">, { action: TimelineEvent; modalityId: Id<"modalityConfigs"> }>();
    const newActionMap = new Map<Id<"monitoringEvents">, { action: TimelineEvent; modalityId: Id<"modalityConfigs"> }>();

    // Populate original action map
    originalDataRef.current.forEach(row => {
      row.actions.forEach(action => {
        originalActionMap.set(action.id, { action, modalityId: row.id });
      });
    });

    // Populate new action map and detect changes
    editorData.forEach(row => {
      row.actions.forEach(action => {
        newActionMap.set(action.id, { action, modalityId: row.id });

        const original = originalActionMap.get(action.id);
        if (!original) {
          // New action created - convert back to NFM format
          createdEvents.push({
            _id: action.id,
            startTime: action.start,
            endTime: action.end,
            modalityId: row.id,
            eventType: action.eventType || 'custom',
            severity: action.severity || 'normal',
            title: action.title || 'Timeline Event',
            description: action.description || ''
          });
        } else if (
          original.action.start !== action.start ||
          original.action.end !== action.end ||
          original.modalityId !== row.id
        ) {
          // Existing action modified
          updatedEvents.push({
            _id: action.id,
            startTime: action.start,
            endTime: action.end,
            modalityId: row.id
          });

          // Check if moved between modalities
          if (original.modalityId !== row.id) {
            movedEvents.push({
              eventId: action.id,
              fromModalityId: original.modalityId,
              toModalityId: row.id
            });
          }
        }
      });
    });

    // Detect deleted actions
    originalActionMap.forEach((_, actionId) => {
      if (!newActionMap.has(actionId)) {
        deletedEventIds.push(actionId);
      }
    });

    return {
      hasChanges: createdEvents.length > 0 ||
                 updatedEvents.length > 0 ||
                 deletedEventIds.length > 0,
      createdEvents,
      updatedEvents,
      deletedEventIds,
      movedEvents
    };
  }, [enableChangeTracking, editorData]);

  // Save changes using Convex mutations
  const saveChanges = useCallback(async () => {
    try {
      console.log('Saving timeline changes...');

      // Get the changes that need to be persisted
      const changes = getChanges();

      if (changes.hasChanges) {
        // Handle created events
        for (const createdEvent of changes.createdEvents) {
          if (onEventCreate) {
            await onEventCreate(createdEvent as TimelineEvent, createdEvent.modalityId as string);
          }
        }

        // Handle updated events
        for (const updatedEvent of changes.updatedEvents) {
          if (onEventUpdate) {
            await onEventUpdate(updatedEvent as TimelineEvent);
          }
        }

        // Handle deleted events
        for (const deletedEventId of changes.deletedEventIds) {
          if (onEventDelete) {
            await onEventDelete(deletedEventId);
          }
        }

        console.log(`Timeline changes saved: ${changes.createdEvents.length} created, ${changes.updatedEvents.length} updated, ${changes.deletedEventIds.length} deleted`);
      } else {
        console.log('No timeline changes to save');
      }

      // Update tracking state
      originalDataRef.current = deepClone(editorData);
      setIsDirty(false);
      setHasUnsavedChanges(false);
      setLastSaved(new Date());

      console.log('Timeline changes saved successfully');
    } catch (error) {
      console.error('Failed to save timeline changes:', error);
      throw error;
    }
  }, [editorData, getChanges, onEventCreate, onEventUpdate, onEventDelete]);

  // Auto-save effect
  useEffect(() => {
    if (autoSave && isDirty) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      autoSaveTimeoutRef.current = setTimeout(() => {
        saveChanges();
      }, autoSaveDelay);
    }
  }, [isDirty, autoSave, autoSaveDelay, saveChanges]);

  // Export data as JSON string
  const exportData = useCallback(() => {
    const exportObject = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      editorData,
      effects,
      duration,
      metadata: {
        changeCount,
        lastSaved: lastSaved?.toISOString(),
        isValid,
        errors,
        warnings
      }
    };
    
    return JSON.stringify(exportObject, null, 2);
  }, [editorData, effects, duration, changeCount, lastSaved, isValid, errors, warnings]);

  // Import data from JSON string
  const importData = useCallback((data: string): boolean => {
    try {
      const importObject = JSON.parse(data);
      
      // Validate import structure
      if (!importObject.editorData || !Array.isArray(importObject.editorData)) {
        throw new Error('Invalid import data structure');
      }
      
      // Update data
      setEditorData(importObject.editorData);
      
      if (importObject.effects) {
        setEffects(importObject.effects);
      }
      if (importObject.duration) {
        setDuration(importObject.duration);
      }
      
      // Mark as dirty since this is imported data
      setIsDirty(true);
      setHasUnsavedChanges(true);
      setChangeCount(prev => prev + 1);
      
      return true;
    } catch (error) {
      console.error('Failed to import timeline data:', error);
      return false;
    }
  }, []);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Transformed data
    editorData,
    effects,
    duration,
    
    // State management
    isDirty,
    isValid,
    errors,
    warnings,
    
    // Change tracking
    hasUnsavedChanges,
    lastSaved,
    changeCount,
    
    // Undo/Redo
    canUndo: enableUndo && undoStack.length > 0,
    canRedo: enableUndo && redoStack.length > 0,
    
    // Actions
    updateData,
    saveChanges,
    discardChanges,
    undo,
    redo,
    
    // Validation
    validate,
    
    // Utilities
    getChanges,
    exportData,
    importData
  };
}
