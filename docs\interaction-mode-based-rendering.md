# Interaction Mode Based Event Rendering

## Problem Solved

The diamond renderer was conflicting with the default EditAction bar rendering, causing both to appear simultaneously. The user identified that this creates issues during drag/resize operations and suggested a brilliant solution.

## Solution: Conditional Rendering Based on Interaction Mode

### **Key Insight**
- **Editing Mode**: Show bars with resize handles for interaction
- **View Mode**: Show clean diamonds for small events

This approach:
- ✅ **Avoids rendering conflicts** during drag/resize operations
- ✅ **Provides clear visual feedback** about interaction capabilities
- ✅ **Maintains usability** when editing is enabled
- ✅ **Offers clean aesthetics** when viewing

## Implementation

### 1. **Updated Interface**

Added interaction mode to renderer props:

```typescript
export interface NFMActionRendererProps {
  // ... existing props
  // Interaction mode to determine rendering behavior
  interactionMode?: InteractionMode;
  // ... rest of props
}
```

### 2. **Enhanced Renderer Selection Logic**

```typescript
export function getActionRenderer(
  action: TimelineEvent,
  row: TimelineRow,
  scale: number = 100,
  scaleWidth: number = 100,
  startLeft: number = 10,
  interactionMode?: InteractionMode
): React.ComponentType<NFMActionRendererProps> {
  
  const { width: pixelWidth } = parserTimeToTransform(
    { start: action.start, end: action.end },
    { startLeft, scale, scaleWidth }
  );
  
  // Only use diamond for very short events when editing is DISABLED
  // When editing is enabled, always use bars so users can see resize handles
  if (pixelWidth < 30 && interactionMode !== InteractionMode.EDIT) {
    console.debug('[getActionRenderer] Using DiamondEventRenderer for short event (editing disabled)');
    return DiamondEventRenderer;
  }
  
  if (pixelWidth < 30 && interactionMode === InteractionMode.EDIT) {
    console.debug('[getActionRenderer] Using bar renderer for short event (editing enabled - need resize handles)');
    // Fall through to normal renderer selection for bars with resize handles
  }
  
  // Continue with severity-based and modality renderer selection...
}
```

### 3. **Updated Timeline Editor**

Pass interaction mode to the renderer:

```typescript
const handleEventRender = useCallback((event: TimelineEvent, row: TimelineRow) => {
  return renderTimelineAction(event, row, {
    isSelected: false,
    isHovered: false,
    scale,
    height: finalConfig.rowHeight || 40,
    // Timeline parameters for proper pixel width calculation
    scaleWidth: timelineStaticConfig.scaleWidth,
    startLeft: timelineStaticConfig.startLeft,
    // Pass interaction mode to determine diamond vs bar rendering
    interactionMode: interactionMode,
  });
}, [scale, finalConfig.rowHeight, timelineStaticConfig.scaleWidth, timelineStaticConfig.startLeft, interactionMode]);
```

## Behavior Matrix

| Event Size | Interaction Mode | Renderer Used | Reason |
|------------|------------------|---------------|---------|
| **Large (≥30px)** | Any | Bar Renderer | Normal events always show bars |
| **Small (<30px)** | `VIEW` | DiamondEventRenderer | Clean viewing experience |
| **Small (<30px)** | `EDIT` | Bar Renderer | Need resize handles for editing |
| **Small (<30px)** | `DISABLED` | DiamondEventRenderer | Clean viewing experience |

## Expected Results

### **View Mode (Editing Disabled)**
```
[getActionRenderer] Event: abc123 PixelWidth: 25 InteractionMode: VIEW
[getActionRenderer] Using DiamondEventRenderer for short event (editing disabled)
```
- ✅ Small events show clean diamond shapes
- ✅ No resize handles visible
- ✅ Clean, minimal interface

### **Edit Mode (Editing Enabled)**
```
[getActionRenderer] Event: abc123 PixelWidth: 25 InteractionMode: EDIT
[getActionRenderer] Using bar renderer for short event (editing enabled - need resize handles)
```
- ✅ Small events show bars with resize handles
- ✅ Users can drag and resize events
- ✅ No sudden renderer changes during interaction

## Benefits

### 1. **No Rendering Conflicts**
- Diamond renderer only used when EditAction won't interfere
- Bar renderer used when interaction capabilities are needed

### 2. **Stable During Interactions**
- No sudden changes from diamond to bar during drag/resize
- Consistent visual feedback throughout editing operations

### 3. **Clear Mode Indication**
- Visual appearance indicates interaction capabilities
- Users understand when they can edit vs just view

### 4. **Performance Optimization**
- Avoids complex rendering conflicts
- Simpler logic with clear conditions

## Testing Instructions

### 1. **Test View Mode**
1. Set interaction mode to VIEW or DISABLED
2. Create short events (< 0.3 seconds at scale 100)
3. Should see diamond shapes only
4. Console: "Using DiamondEventRenderer for short event (editing disabled)"

### 2. **Test Edit Mode**
1. Enable editing mode (Allow Edit toggle ON)
2. Same short events should now show bars
3. Should be able to resize and drag events
4. Console: "Using bar renderer for short event (editing enabled - need resize handles)"

### 3. **Test Mode Switching**
1. Create short events in view mode (diamonds visible)
2. Enable editing mode
3. Events should switch to bars
4. Disable editing mode
5. Events should switch back to diamonds

## Files Modified

- `types/timelineEditor.ts`: Added `interactionMode` to `NFMActionRendererProps`
- `components/timeline/NFMTimelineEditor.tsx`: Pass interaction mode to renderer
- `components/timeline/effects/eventRenderers.tsx`: 
  - Import `InteractionMode` enum
  - Update `getActionRenderer` to consider interaction mode
  - Update `renderTimelineAction` to pass interaction mode

This approach elegantly solves the rendering conflict while providing the best user experience for both viewing and editing scenarios.
