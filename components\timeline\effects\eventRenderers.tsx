/**
 * Custom action renderers for different event types in the NFM timeline
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { NFMActionRendererProps } from '@/types/timelineEditor';
import { TimelineEvent, TimelineRow } from '../interface/timeline';
import {
  AlertTriangle,
  AlertCircle,
  Info,
  Zap,
  Activity,
  Heart,
  Brain,
  Mic,
  Video,
  Settings
} from 'lucide-react';

/**
 * Utility function to adjust color brightness
 */
function adjustColorBrightness(color: string, amount: number): string {
  // Remove # if present
  const hex = color.replace('#', '');

  // Parse RGB values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Adjust brightness
  const newR = Math.max(0, Math.min(255, r + amount));
  const newG = Math.max(0, Math.min(255, g + amount));
  const newB = Math.max(0, Math.min(255, b + amount));

  // Convert back to hex
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

/**
 * Base action renderer with common styling and behavior
 * NOTE: This component should NOT handle events directly - events are handled by EditAction wrapper
 */
export function BaseActionRenderer({
  action,
  row,
  isSelected,
  isHovered,
  scale,
  height,
  children,
  className,
  isResizing = false,
  isDragging = false
}: Omit<NFMActionRendererProps, 'onClick' | 'onDoubleClick' | 'onContextMenu'> & {
  children?: React.ReactNode;
  className?: string;
  isResizing?: boolean;
  isDragging?: boolean;
}) {
  const durationInSeconds = action.end - action.start;
  const pixelWidth = durationInSeconds * scale;
  const minWidth = 20; // Minimum width for visibility
  const actualWidth = Math.max(pixelWidth, minWidth);

  console.debug('[BaseActionRenderer] Event:', action.id, 'Duration:', durationInSeconds, 'PixelWidth:', pixelWidth, 'ActualWidth:', actualWidth);

  // Get event severity for styling
  const severity = action.severity || 'normal';

  // Get modality color from row
  const modalityColor = row.colorCode || action.modalityColor || '#6b7280'; // Default gray
  const modalityColorDark = adjustColorBrightness(modalityColor, -20); // Darker border

  // Always use modality color unless explicitly disabled
  // Only legacy modality renderers (EEG, EMG, etc.) should override with hardcoded colors
  const useModalityColor = !className?.includes('bg-purple-') &&
                           !className?.includes('bg-green-') &&
                           !className?.includes('bg-red-') &&
                           !className?.includes('bg-indigo-') &&
                           !className?.includes('bg-orange-');

  return (
    <div
      data-action-id={action.id}
      className={cn(
        'timeline-action',
        'timeline-editor-action',
        // Add effectId-based CSS class for styling system
        action.effectId && `timeline-editor-action-effect-${action.effectId}`,
        'absolute top-0 rounded-sm border cursor-pointer transition-all duration-200',
        'flex items-center justify-start px-1 text-xs font-medium text-white',
        'hover:shadow-md hover:z-10',
        // Severity-based colors (only if not using modality color)
        !useModalityColor && {
          'bg-red-500 border-red-600': severity === 'critical',
          'bg-amber-400 border-amber-500': severity === 'warning',
          'bg-gray-400 border-gray-500': severity === 'normal'
        },
        // Selection state
        {
          'ring-2 ring-blue-300 ring-offset-1': isSelected,
          'brightness-110': isHovered
        },
        // Resize/drag state classes to prevent visual artifacts
        {
          'timeline-action-resizing': isResizing,
          'timeline-action-dragging': isDragging
        },
        // Animation classes (added by effects)
        'timeline-action-base',
        className
      )}
      style={{
        width: `${actualWidth}px`,
        height: `${height - 4}px`,
        left: `${action.start * scale}px`,
        top: '2px',
        // Use modality color if no specific background class is provided
        ...(useModalityColor && {
          backgroundColor: modalityColor,
          borderColor: modalityColorDark
        })
      }}
      // DO NOT add event handlers here - they are handled by EditAction wrapper
      // onClick={onClick}
      // onDoubleClick={onDoubleClick}
      // onContextMenu={onContextMenu}
      title={`${action.title || 'Event'} (${action.start.toFixed(1)}s - ${action.end.toFixed(1)}s)`}
    >
      {children}
    </div>
  );
}

/**
 * Alarm event renderer with pulsing effect
 */
export function AlarmEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-alarm animate-pulse"
    >
      <AlertTriangle className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Alarm'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Critical event renderer with enhanced styling
 */
export function CriticalEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-critical shadow-lg"
    >
      <Zap className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate font-bold">
        {props.action.title || 'Critical'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Warning event renderer
 */
export function WarningEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-warning"
    >
      <AlertCircle className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Warning'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Info event renderer
 */
export function InfoEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-info"
    >
      <Info className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Info'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Modality-specific renderers
 */

/**
 * EEG event renderer
 */
export function EEGEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-eeg bg-purple-500 border-purple-600"
    >
      <Brain className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'EEG'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * EMG event renderer
 */
export function EMGEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-emg bg-green-500 border-green-600"
    >
      <Activity className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'EMG'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * ECG event renderer
 */
export function ECGEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-ecg bg-red-500 border-red-600"
    >
      <Heart className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'ECG'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Video event renderer
 */
export function VideoEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-video bg-indigo-500 border-indigo-600"
    >
      <Video className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Video'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Audio event renderer
 */
export function AudioEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-audio bg-orange-500 border-orange-600"
    >
      <Mic className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Audio'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Custom event renderer for unknown types
 */
export function CustomEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-custom"
    >
      <Settings className="w-3 h-3 mr-1 flex-shrink-0" />
      <span className="truncate">
        {props.action.title || 'Custom'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Diamond event renderer for very short events
 * NOTE: This component should NOT handle events directly - events are handled by EditAction wrapper
 */
export function DiamondEventRenderer(props: NFMActionRendererProps) {
  const { action, row, isSelected, isHovered, scale, height } = props;

  // Get modality color from row
  const modalityColor = row.colorCode || action.modalityColor || '#6b7280';
  const modalityColorDark = adjustColorBrightness(modalityColor, -20);

  // Get event severity for styling
  const severity = action.severity || 'normal';

  // Calculate position - center the diamond on the event start time
  const leftPosition = action.start * scale;
  const topPosition = (height - 20) / 2; // Center vertically in the row

  console.debug('[DiamondEventRenderer] Rendering diamond for event:', action.id, 'at position:', leftPosition);

  return (
    <div
      data-action-id={action.id}
      className={cn(
        'timeline-action timeline-action-diamond',
        'timeline-editor-action',
        // Add effectId-based CSS class for styling system
        action.effectId && `timeline-editor-action-effect-${action.effectId}`,
        'absolute cursor-pointer transition-all duration-200',
        'flex items-center justify-center text-xs font-bold',
        'hover:shadow-lg hover:z-20',
        // Selection state
        {
          'ring-2 ring-blue-300 ring-offset-1': isSelected,
          'brightness-110': isHovered
        },
        // Severity-based animation classes
        {
          'timeline-action-critical': severity === 'critical',
          'timeline-action-warning': severity === 'warning'
        }
      )}
      style={{
        backgroundColor: modalityColor,
        borderColor: modalityColorDark,
        border: `2px solid ${modalityColorDark}`,
        left: `${leftPosition}px`,
        top: `${topPosition}px`,
        // CSS custom properties for dynamic styling
        ['--modality-color']: modalityColor,
        ['--modality-color-dark']: modalityColorDark
      } as React.CSSProperties & Record<string, string>}
      // DO NOT add event handlers here - they are handled by EditAction wrapper
      title={`${action.title || 'Event'} (${action.start.toFixed(1)}s)`}
    >
      <div className="timeline-action-content">
        {/* Show first letter of event type or severity indicator */}
        {severity === 'critical' ? '!' :
         severity === 'warning' ? '⚠' :
         action.title?.charAt(0) || 'E'}
      </div>
    </div>
  );
}

/**
 * Compact event renderer for small actions
 */
export function CompactEventRenderer(props: NFMActionRendererProps) {
  const durationInSeconds = props.action.end - props.action.start;
  const pixelWidth = durationInSeconds * props.scale;
  const isVerySmall = pixelWidth < 30;

  if (isVerySmall) {
    return <DiamondEventRenderer {...props} />;
  }

  return <CustomEventRenderer {...props} />;
}

/**
 * Progress indicator renderer for ongoing events
 */
export function ProgressEventRenderer(props: NFMActionRendererProps) {
  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-progress relative overflow-hidden"
    >
      <div 
        className="absolute inset-0 bg-white opacity-20 transition-all duration-300"
        style={{
          width: 'var(--progress, 0%)',
          transformOrigin: 'left'
        }}
      />
      <span className="truncate relative z-10">
        {props.action.title || 'Progress'}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Enhanced modality event renderer that uses database colors
 */
export function ModalityEventRenderer(props: NFMActionRendererProps) {
  const { action, row, scale } = props;
  const durationInSeconds = action.end - action.start;
  const pixelWidth = durationInSeconds * scale;
  const isVerySmall = pixelWidth < 30;

  // Use diamond shape for very short events
  if (isVerySmall) {
    console.debug('[ModalityEventRenderer] Event too small, using DiamondEventRenderer');
    return <DiamondEventRenderer {...props} />;
  }

  // Get modality-specific icon
  const getModalityIcon = (modalityName: string) => {
    const name = modalityName.toLowerCase();
    switch (name) {
      case 'eeg': return <Brain className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'emg': return <Activity className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'mep': return <Zap className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'ssep': return <Activity className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'ecg': return <Heart className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'video': return <Video className="w-3 h-3 mr-1 flex-shrink-0" />;
      case 'audio': return <Mic className="w-3 h-3 mr-1 flex-shrink-0" />;
      default: return <Settings className="w-3 h-3 mr-1 flex-shrink-0" />;
    }
  };

  const modalityName = row.name || row.displayName || 'Unknown';
  const icon = getModalityIcon(modalityName);

  return (
    <BaseActionRenderer
      {...props}
      className="timeline-action-modality"
    >
      {icon}
      <span className="truncate">
        {action.title || action.eventType || modalityName}
      </span>
    </BaseActionRenderer>
  );
}

/**
 * Main renderer function that selects appropriate renderer based on action type
 */
export function getActionRenderer(
  action: TimelineEvent,
  row: TimelineRow,
  scale: number = 100
): React.ComponentType<NFMActionRendererProps> {
  // Calculate pixel width correctly - scale represents pixels per second
  const durationInSeconds = action.end - action.start;
  const pixelWidth = durationInSeconds * scale;

  console.debug('[getActionRenderer] Event:', action.id, 'Duration:', durationInSeconds, 'Scale:', scale, 'PixelWidth:', pixelWidth);

  // Always use diamond for very short events (less than 30px width)
  if (pixelWidth < 30) {
    console.debug('[getActionRenderer] Using DiamondEventRenderer for short event');
    return DiamondEventRenderer;
  }

  // Check for event type renderers first (for specific event types with severity)
  const eventType = action.eventType;
  const severity = action.severity || 'normal';

  // Only use severity-specific renderers for actual severity events
  // These renderers will still use modality colors due to the updated BaseActionRenderer logic
  if (severity === 'critical' || eventType === 'critical') {
    console.debug('[getActionRenderer] Using CriticalEventRenderer');
    return CriticalEventRenderer;
  }
  if (severity === 'warning' || eventType === 'warning') {
    console.debug('[getActionRenderer] Using WarningEventRenderer');
    return WarningEventRenderer;
  }
  if (eventType === 'info') {
    console.debug('[getActionRenderer] Using InfoEventRenderer');
    return InfoEventRenderer;
  }

  // For all other events, use the modality renderer with database colors
  // This ensures all events use the colorCode from the database
  console.debug('[getActionRenderer] Using ModalityEventRenderer');
  return ModalityEventRenderer;
}

/**
 * Render action with appropriate renderer
 */
export function renderTimelineAction(
  action: TimelineEvent,
  row: TimelineRow,
  props: Omit<NFMActionRendererProps, 'action' | 'row' | 'onClick' | 'onDoubleClick' | 'onContextMenu'>
): React.ReactElement {
  const Renderer = getActionRenderer(action, row, props.scale);
  return <Renderer action={action} row={row} {...props} />;
}
